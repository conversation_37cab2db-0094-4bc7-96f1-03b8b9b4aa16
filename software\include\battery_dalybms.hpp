#pragma once
#include <stdint.h>
//--------------------------------------------------------------//
// Daly BMS ID
//--------------------------------------------------------------//
#define DALY_BMS_CAN_MESSAGE_FILTER_MASK (DALY_BMS_PRIORITY << 28)
#define DALY_BMS_CAN_MESSAGE_FILTER_ACCEPTANCE (DALY_BMS_PRIORITY << 28)
#define LOGIC_SYSTEM_BATTERY_BMS_ADDRESS 0x01     // 24V Battery
#define ACTUATION_SYSTEM_BATTERY_BMS_ADDRESS 0x02 // 48V Battery

#define DALY_BMS_PRIORITY 0x18
#define DALY_BMS_PC_ADDRESS 0x40
#define DALY_BMS_GENERATE_ID(DATA_ID, BMS_ADDRESS)                             \
  ((uint32_t)(DALY_BMS_PRIORITY << 24) | (DATA_ID << 16) |                     \
   (BMS_ADDRESS << 8) | DALY_BMS_PC_ADDRESS)

#define DALY_BMS_GET_DATA_ID(CAN_ID) ((uint8_t)((CAN_ID >> 16) & 0XFF))

#define DALY_BMS_GET_BMS_ADDRESS(CAN_ID) ((uint8_t)(CAN_ID & 0XFF))

// Daly BMS Data IDs
enum class DALY_BMS_DID {
  SOC_DATA_ID = 0x90U,
  MIN_MAX_VOLTAGE_DATA_ID = 0x91U,
  MIN_MAX_TEMPERATURE_DATA_ID = 0x92U,
  CHARGE_DISCHARGE_MOS_STATUS_DATA_ID = 0x93U,
  STATUS_INFORMATION_1 = 0x94U,
  CELL_VOLTAGE_1_48 = 0x95U,
  CELL_TEMPERATURE_1_16 = 0x96U,
  CELL_BALANCE_STATE_1_48 = 0x97U,
  CELL_BATTERY_FAILURE_STATUS = 0x98U,
};
#define DALY_BMS_DATA_ID_COUNT 9U
const uint8_t DALY_BMS_DATA_IDS[DALY_BMS_DATA_ID_COUNT] = {
    (uint8_t)DALY_BMS_DID::SOC_DATA_ID,
    (uint8_t)DALY_BMS_DID::MIN_MAX_VOLTAGE_DATA_ID,
    (uint8_t)DALY_BMS_DID::MIN_MAX_TEMPERATURE_DATA_ID,
    (uint8_t)DALY_BMS_DID::CHARGE_DISCHARGE_MOS_STATUS_DATA_ID,
    (uint8_t)DALY_BMS_DID::STATUS_INFORMATION_1,
    (uint8_t)DALY_BMS_DID::CELL_VOLTAGE_1_48,
    (uint8_t)DALY_BMS_DID::CELL_TEMPERATURE_1_16,
    (uint8_t)DALY_BMS_DID::CELL_BALANCE_STATE_1_48,
    (uint8_t)DALY_BMS_DID::CELL_BATTERY_FAILURE_STATUS,
};

void request_battery_information(const uint8_t battery_id);

// CAN Data Parser
void parse_battery_soc_data(const uint8_t message[8], const uint8_t bms_address,
                            float msg_logic_battery_ros[3],
                            float msg_actuation_battery_ros[3]);
